const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';

// Article types based on backend response
export interface Article {
  id: string;
  title: string;
  content: string;
  publishedAt: string;
  updatedAt: string;
  imageURL?: string;
  author: {
    id: string;
    name: string;
    email: string;
  };
  comments: Array<{
    id: string;
    content: string;
    createdAt: string;
    editedAt?: string;
    author: {
      id: string;
      name: string;
      email: string;
    };
  }>;
  category?: {
    id: string;
    name: string;
  };
  tags?: Array<{
    id: string;
    name: string;
  }>;
}

// Frontend article card type
export interface ArticleCard {
  id: string;
  title: string;
  excerpt: string;
  date: string;
  slug: string;
  author: string;
  category?: string;
  tags?: string[];
  viewCount?: number;
  imageURL?: string;
}

// API Functions
export const articlesApi = {
  // Get all articles
  async getAll(): Promise<Article[]> {
    try {
      const response = await fetch(`${API_BASE}/api/posts`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-store', // Ensure fresh data
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch articles: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching articles:', error);
      throw error;
    }
  },

  // Get single article by ID
  async getById(id: string): Promise<Article | null> {
    try {
      const response = await fetch(`${API_BASE}/api/posts/${id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-store',
      });

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`Failed to fetch article: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching article:', error);
      throw error;
    }
  },

  // Create new article (admin only)
  async create(articleData: Partial<Article>, token: string): Promise<Article> {
    try {
      const response = await fetch(`${API_BASE}/api/posts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(articleData),
      });

      if (!response.ok) {
        throw new Error(`Failed to create article: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating article:', error);
      throw error;
    }
  },

  // Update article (admin only)
  async update(id: string, articleData: Partial<Article>, token: string): Promise<Article> {
    try {
      const response = await fetch(`${API_BASE}/api/posts/update/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(articleData),
      });

      if (!response.ok) {
        throw new Error(`Failed to update article: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating article:', error);
      throw error;
    }
  },

  // Delete article (admin only)
  async delete(id: string, token: string): Promise<void> {
    try {
      const response = await fetch(`${API_BASE}/api/posts/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to delete article: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error deleting article:', error);
      throw error;
    }
  },
};

// Utility function to convert backend Article to frontend ArticleCard
export function transformArticleToCard(article: Article): ArticleCard {
  // Create excerpt from content (first 150 characters)
  const excerpt = article.content
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .substring(0, 150) + (article.content.length > 150 ? '...' : '');

  // Create slug from title
  const slug = article.title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');

  // Format date
  const date = new Date(article.publishedAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });

  return {
    id: article.id,
    title: article.title,
    excerpt,
    date,
    slug,
    author: article.author.name,
    category: article.category?.name,
    tags: article.tags?.map(tag => tag.name),
    imageURL: article.imageURL,
  };
}

// Categories API
export const categoriesApi = {
  async getAll() {
    try {
      const response = await fetch(`${API_BASE}/api/categories`, {
        cache: 'no-store',
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch categories: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }
  },
};
