import React from 'react';
import ArticleCard from '@/components/articleCard/page';
import { articlesApi, transformArticleToCard } from '@/lib/api';

async function getArticles() {
  try {
    const articles = await articlesApi.getAll();
    return articles.map(transformArticleToCard);
  } catch (error) {
    console.error('Failed to fetch articles:', error);
    return [];
  }
}

export default async function Home() {
  const articles = await getArticles();

  return (
    <main className="container mx-auto px-4 py-8">
      <div className="text-center mb-12">
        <h1 className="text-4xl md:text-6xl font-bold mb-4">
          Welcome to Our Blog
        </h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Discover insights, tutorials, and thoughts on web development, design,
          and technology.
        </p>
      </div>

      {articles.length > 0 ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {articles.map((article) => (
            <ArticleCard key={article.id} article={article} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-lg text-muted-foreground">
            No articles found. Please check back later!
          </p>
        </div>
      )}
    </main>
  );
}
