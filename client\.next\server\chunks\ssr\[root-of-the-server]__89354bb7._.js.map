{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,IAAA,iLAAG,EACvB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,kLAAI,GAAG;IAE9B,qBACE,wPAAC;QACC,aAAU;QACV,WAAW,IAAA,mIAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/articleCard/page.tsx"], "sourcesContent": ["import React from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { Eye, User } from 'lucide-react';\r\nimport { Badge } from '@/components/ui/badge';\r\n\r\ninterface ArticleCardProps {\r\n  article: {\r\n    id: string;\r\n    title: string;\r\n    excerpt: string;\r\n    date: string;\r\n    slug: string;\r\n    author: string;\r\n    category?: string;\r\n    tags?: string[];\r\n    viewCount?: number;\r\n    imageURL?: string;\r\n  };\r\n}\r\n\r\nconst ArticleCard = ({ article }: ArticleCardProps) => {\r\n  const {\r\n    title,\r\n    excerpt,\r\n    date,\r\n    slug,\r\n    author,\r\n    category = 'General',\r\n    tags = [],\r\n    viewCount = 0,\r\n    imageURL,\r\n  } = article;\r\n  return (\r\n    <Link href={`/articles/${slug}`}>\r\n      <article className=\"group bg-card border border-border p-6 hover:gradient-border transition-all duration-300 h-full flex flex-col\">\r\n        {/* Image */}\r\n        {imageURL && (\r\n          <div className=\"mb-4 overflow-hidden rounded-lg relative h-48\">\r\n            <Image\r\n              src={imageURL}\r\n              alt={title}\r\n              fill\r\n              className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\r\n              sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        <div className=\"space-y-4 flex-1\">\r\n          {/* Category and View Count */}\r\n          <div className=\"flex items-center justify-between\">\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              {category}\r\n            </Badge>\r\n            {viewCount > 0 && (\r\n              <div className=\"flex items-center space-x-1 text-xs text-muted-foreground\">\r\n                <Eye className=\"h-3 w-3\" />\r\n                <span>{viewCount.toLocaleString()}</span>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Title */}\r\n          <h2 className=\"text-xl font-bold text-card-foreground group-hover:gradient-text transition-colors line-clamp-2\">\r\n            {title}\r\n          </h2>\r\n\r\n          {/* Excerpt */}\r\n          <p className=\"text-muted-foreground leading-relaxed line-clamp-3 flex-1\">\r\n            {excerpt}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Footer */}\r\n        <div className=\"mt-4 pt-4 border-t border-border space-y-3\">\r\n          {/* Author and Date */}\r\n          <div className=\"flex items-center justify-between text-sm\">\r\n            <div className=\"flex items-center space-x-2 text-secondary-foreground\">\r\n              <User className=\"h-3 w-3\" />\r\n              <span>{author}</span>\r\n            </div>\r\n            <time className=\"text-secondary-foreground font-mono\">{date}</time>\r\n          </div>\r\n\r\n          {/* Tags */}\r\n          {tags.length > 0 && (\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              {tags.slice(0, 3).map((tag) => (\r\n                <Badge key={tag} variant=\"outline\" className=\"text-xs\">\r\n                  {tag}\r\n                </Badge>\r\n              ))}\r\n              {tags.length > 3 && (\r\n                <Badge variant=\"outline\" className=\"text-xs\">\r\n                  +{tags.length - 3}\r\n                </Badge>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </article>\r\n    </Link>\r\n  );\r\n};\r\n\r\nexport default ArticleCard;\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAAA;AACA;;;;;;AAiBA,MAAM,cAAc,CAAC,EAAE,OAAO,EAAoB;IAChD,MAAM,EACJ,KAAK,EACL,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,WAAW,SAAS,EACpB,OAAO,EAAE,EACT,YAAY,CAAC,EACb,QAAQ,EACT,GAAG;IACJ,qBACE,wPAAC,iLAAI;QAAC,MAAM,CAAC,UAAU,EAAE,MAAM;kBAC7B,cAAA,wPAAC;YAAQ,WAAU;;gBAEhB,0BACC,wPAAC;oBAAI,WAAU;8BACb,cAAA,wPAAC,kJAAK;wBACJ,KAAK;wBACL,KAAK;wBACL,IAAI;wBACJ,WAAU;wBACV,OAAM;;;;;;;;;;;8BAK<PERSON>,wPAAC;oBAAI,WAAU;;sCAEb,wPAAC;4BAAI,WAAU;;8CACb,wPAAC,oJAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAClC;;;;;;gCAEF,YAAY,mBACX,wPAAC;oCAAI,WAAU;;sDACb,wPAAC,iNAAG;4CAAC,WAAU;;;;;;sDACf,wPAAC;sDAAM,UAAU,cAAc;;;;;;;;;;;;;;;;;;sCAMrC,wPAAC;4BAAG,WAAU;sCACX;;;;;;sCAIH,wPAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;8BAKL,wPAAC;oBAAI,WAAU;;sCAEb,wPAAC;4BAAI,WAAU;;8CACb,wPAAC;oCAAI,WAAU;;sDACb,wPAAC,oNAAI;4CAAC,WAAU;;;;;;sDAChB,wPAAC;sDAAM;;;;;;;;;;;;8CAET,wPAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;wBAIxD,KAAK,MAAM,GAAG,mBACb,wPAAC;4BAAI,WAAU;;gCACZ,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBACrB,wPAAC,oJAAK;wCAAW,SAAQ;wCAAU,WAAU;kDAC1C;uCADS;;;;;gCAIb,KAAK,MAAM,GAAG,mBACb,wPAAC,oJAAK;oCAAC,SAAQ;oCAAU,WAAU;;wCAAU;wCACzC,KAAK,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlC;uCAEe", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/lib/api.ts"], "sourcesContent": ["const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';\n\n// Article types based on backend response\nexport interface Article {\n  id: string;\n  title: string;\n  content: string;\n  publishedAt: string;\n  updatedAt: string;\n  imageURL?: string;\n  author: {\n    id: string;\n    name: string;\n    email: string;\n  };\n  comments: Array<{\n    id: string;\n    content: string;\n    createdAt: string;\n    editedAt?: string;\n    author: {\n      id: string;\n      name: string;\n      email: string;\n    };\n  }>;\n  category?: {\n    id: string;\n    name: string;\n  };\n  tags?: Array<{\n    id: string;\n    name: string;\n  }>;\n}\n\n// Frontend article card type\nexport interface ArticleCard {\n  id: string;\n  title: string;\n  excerpt: string;\n  date: string;\n  slug: string;\n  author: string;\n  category?: string;\n  tags?: string[];\n  viewCount?: number;\n  imageURL?: string;\n}\n\n// API Functions\nexport const articlesApi = {\n  // Get all articles\n  async getAll(): Promise<Article[]> {\n    try {\n      const response = await fetch(`${API_BASE}/api/posts`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        cache: 'no-store', // Ensure fresh data\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch articles: ${response.statusText}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error('Error fetching articles:', error);\n      throw error;\n    }\n  },\n\n  // Get single article by ID\n  async getById(id: string): Promise<Article | null> {\n    try {\n      const response = await fetch(`${API_BASE}/api/posts/${id}`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        cache: 'no-store',\n      });\n\n      if (!response.ok) {\n        if (response.status === 404) {\n          return null;\n        }\n        throw new Error(`Failed to fetch article: ${response.statusText}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error('Error fetching article:', error);\n      throw error;\n    }\n  },\n\n  // Create new article (admin only)\n  async create(articleData: Partial<Article>, token: string): Promise<Article> {\n    try {\n      const response = await fetch(`${API_BASE}/api/posts`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`,\n        },\n        body: JSON.stringify(articleData),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to create article: ${response.statusText}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error('Error creating article:', error);\n      throw error;\n    }\n  },\n\n  // Update article (admin only)\n  async update(id: string, articleData: Partial<Article>, token: string): Promise<Article> {\n    try {\n      const response = await fetch(`${API_BASE}/api/posts/update/${id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`,\n        },\n        body: JSON.stringify(articleData),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to update article: ${response.statusText}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error('Error updating article:', error);\n      throw error;\n    }\n  },\n\n  // Delete article (admin only)\n  async delete(id: string, token: string): Promise<void> {\n    try {\n      const response = await fetch(`${API_BASE}/api/posts/${id}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to delete article: ${response.statusText}`);\n      }\n    } catch (error) {\n      console.error('Error deleting article:', error);\n      throw error;\n    }\n  },\n};\n\n// Utility function to convert backend Article to frontend ArticleCard\nexport function transformArticleToCard(article: Article): ArticleCard {\n  // Create excerpt from content (first 150 characters)\n  const excerpt = article.content\n    .replace(/<[^>]*>/g, '') // Remove HTML tags\n    .substring(0, 150) + (article.content.length > 150 ? '...' : '');\n\n  // Create slug from title\n  const slug = article.title\n    .toLowerCase()\n    .replace(/[^a-z0-9]+/g, '-')\n    .replace(/(^-|-$)/g, '');\n\n  // Format date\n  const date = new Date(article.publishedAt).toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  });\n\n  return {\n    id: article.id,\n    title: article.title,\n    excerpt,\n    date,\n    slug,\n    author: article.author.name,\n    category: article.category?.name,\n    tags: article.tags?.map(tag => tag.name),\n    imageURL: article.imageURL,\n  };\n}\n\n// Categories API\nexport const categoriesApi = {\n  async getAll() {\n    try {\n      const response = await fetch(`${API_BASE}/api/categories`, {\n        cache: 'no-store',\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch categories: ${response.statusText}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n      throw error;\n    }\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA,MAAM,WAAW,6DAAmC;AAmD7C,MAAM,cAAc;IACzB,mBAAmB;IACnB,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,SAAS,UAAU,CAAC,EAAE;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,OAAO;YACT;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,UAAU,EAAE;YACpE;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA,2BAA2B;IAC3B,MAAM,SAAQ,EAAU;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,SAAS,WAAW,EAAE,IAAI,EAAE;gBAC1D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,OAAO;YACT;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,IAAI,SAAS,MAAM,KAAK,KAAK;oBAC3B,OAAO;gBACT;gBACA,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,kCAAkC;IAClC,MAAM,QAAO,WAA6B,EAAE,KAAa;QACvD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,SAAS,UAAU,CAAC,EAAE;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,UAAU,EAAE;YACpE;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,MAAM,QAAO,EAAU,EAAE,WAA6B,EAAE,KAAa;QACnE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,SAAS,kBAAkB,EAAE,IAAI,EAAE;gBACjE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,UAAU,EAAE;YACpE;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,MAAM,QAAO,EAAU,EAAE,KAAa;QACpC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,SAAS,WAAW,EAAE,IAAI,EAAE;gBAC1D,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,UAAU,EAAE;YACpE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;AACF;AAGO,SAAS,uBAAuB,OAAgB;IACrD,qDAAqD;IACrD,MAAM,UAAU,QAAQ,OAAO,CAC5B,OAAO,CAAC,YAAY,IAAI,mBAAmB;KAC3C,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ,OAAO,CAAC,MAAM,GAAG,MAAM,QAAQ,EAAE;IAEjE,yBAAyB;IACzB,MAAM,OAAO,QAAQ,KAAK,CACvB,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,YAAY;IAEvB,cAAc;IACd,MAAM,OAAO,IAAI,KAAK,QAAQ,WAAW,EAAE,kBAAkB,CAAC,SAAS;QACrE,MAAM;QACN,OAAO;QACP,KAAK;IACP;IAEA,OAAO;QACL,IAAI,QAAQ,EAAE;QACd,OAAO,QAAQ,KAAK;QACpB;QACA;QACA;QACA,QAAQ,QAAQ,MAAM,CAAC,IAAI;QAC3B,UAAU,QAAQ,QAAQ,EAAE;QAC5B,MAAM,QAAQ,IAAI,EAAE,IAAI,CAAA,MAAO,IAAI,IAAI;QACvC,UAAU,QAAQ,QAAQ;IAC5B;AACF;AAGO,MAAM,gBAAgB;IAC3B,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,SAAS,eAAe,CAAC,EAAE;gBACzD,OAAO;YACT;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,SAAS,UAAU,EAAE;YACtE;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/app/page.tsx"], "sourcesContent": ["import React from 'react';\r\nimport ArticleCard from '@/components/articleCard/page';\r\nimport { articlesApi, transformArticleToCard } from '@/lib/api';\r\n\r\nasync function getArticles() {\r\n  try {\r\n    const articles = await articlesApi.getAll();\r\n    return articles.map(transformArticleToCard);\r\n  } catch (error) {\r\n    console.error('Failed to fetch articles:', error);\r\n    return [];\r\n  }\r\n}\r\n\r\nexport default async function Home() {\r\n  const articles = await getArticles();\r\n\r\n  return (\r\n    <main className=\"container mx-auto px-4 py-8\">\r\n      <div className=\"text-center mb-12\">\r\n        <h1 className=\"text-4xl md:text-6xl font-bold mb-4\">\r\n          Welcome to Our Blog\r\n        </h1>\r\n        <p className=\"text-xl text-muted-foreground max-w-2xl mx-auto\">\r\n          Discover insights, tutorials, and thoughts on web development, design,\r\n          and technology.\r\n        </p>\r\n      </div>\r\n\r\n      {articles.length > 0 ? (\r\n        <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\r\n          {articles.map((article) => (\r\n            <ArticleCard key={article.id} article={article} />\r\n          ))}\r\n        </div>\r\n      ) : (\r\n        <div className=\"text-center py-12\">\r\n          <p className=\"text-lg text-muted-foreground\">\r\n            No articles found. Please check back later!\r\n          </p>\r\n        </div>\r\n      )}\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,eAAe;IACb,IAAI;QACF,MAAM,WAAW,MAAM,0IAAW,CAAC,MAAM;QACzC,OAAO,SAAS,GAAG,CAAC,qJAAsB;IAC5C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,EAAE;IACX;AACF;AAEe,eAAe;IAC5B,MAAM,WAAW,MAAM;IAEvB,qBACE,wPAAC;QAAK,WAAU;;0BACd,wPAAC;gBAAI,WAAU;;kCACb,wPAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAGpD,wPAAC;wBAAE,WAAU;kCAAkD;;;;;;;;;;;;YAMhE,SAAS,MAAM,GAAG,kBACjB,wPAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,wPAAC,8JAAW;wBAAkB,SAAS;uBAArB,QAAQ,EAAE;;;;;;;;;qCAIhC,wPAAC;gBAAI,WAAU;0BACb,cAAA,wPAAC;oBAAE,WAAU;8BAAgC;;;;;;;;;;;;;;;;;AAOvD", "debugId": null}}]}